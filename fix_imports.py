#!/usr/bin/env python3
"""
Script to fix import issues in player.py
"""

import re

def fix_imports_in_file(file_path):
    """Fix import issues in the specified file"""
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix imports
    replacements = [
        # Fix database imports
        (r'from app\.utils\.database import', 'from utils.database import'),
        
        # Fix app imports
        (r'import app\.app', 'import app'),
        (r'from app\.app import', 'from app import'),
        
        # Fix app.app references
        (r'app\.app\.', 'app.'),
        
        # Fix fallback imports that are still using app.utils.database
        (r'from app\.utils\.database import track_test_execution', 'from utils.database import track_test_execution'),
        (r'from app\.utils\.database import get_test_case_by_id', 'from utils.database import get_test_case_by_id'),
        (r'from app\.utils\.database import save_test_run_data', 'from utils.database import save_test_run_data'),
        (r'from app\.utils\.database import check_screenshot_exists', 'from utils.database import check_screenshot_exists'),
        (r'from app\.utils\.database import save_screenshot_info', 'from utils.database import save_screenshot_info'),
        (r'from app\.utils\.database import get_current_test_run_id', 'from utils.database import get_current_test_run_id'),
    ]
    
    # Apply replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed imports in {file_path}")

if __name__ == "__main__":
    fix_imports_in_file("app/utils/player.py")
    print("Import fixes completed!")
