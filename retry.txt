export-run.js:382 Export Manager: Initializing
export-run.js:93 Export Manager: Looking for button container... <div class=​"d-flex justify-content-end mb-3">​…​</div>​flex
export-run.js:116 Export Manager: <PERSON><PERSON> inserted before Clear All button
export-run.js:121 Export Manager: <PERSON><PERSON> created and added to UI
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:397 Export Manager: Debug functions available at window.debugExportManager
(index):2717 Page loaded, checking for persisted environment...
(index):2718 Session storage active environment: null
main.js:9 DOM content loaded. Initializing App and modules...
main.js:18 ElementInteractions module should be loaded globally.
main.js:187 [AppiumAutomationApp CONSTRUCTOR] New instance created with ID: 1750134155476_0.8229877373229156
main.js:191 Generated client session ID: client_1750134155476_68r2mr2lc_1750134155476_68r2mr2lc
main.js:288 ActionManager instantiated in App constructor.
main.js:302 MultiStepAction instantiated in App constructor.
main.js:321 RepeatStepsAction class not defined when App constructor ran.
AppiumAutomationApp @ main.js:321
main.js:329 HookAction instantiated in App constructor.
fallback-locators.js:27 Add tap fallback locator button not found
initEventListeners @ fallback-locators.js:27
fallback-locators.js:13 FallbackLocatorsManager initialized
main.js:342 FallbackLocatorsManager instantiated in App constructor.
tap-fallback-manager.js:14 TapFallbackManager initialized
main.js:355 TapFallbackManager instantiated in App constructor.
main.js:383 Initializing Appium Automation App...
main.js:696 Initializing Sortable on actionsList element
main.js:755 Sortable initialized successfully
main.js:627 Event listener for retry/remove on actionsList attached.
main.js:2806 Loading reference images for action type: tap, target: tapImageFilename
main.js:2806 Loading reference images for action type: tap, target: general
main.js:2806 Loading reference images for action type: doubleTap, target: general
main.js:2806 Loading reference images for action type: clickImageAirtest, target: general
main.js:2806 Loading reference images for action type: waitImageAirtest, target: general
main.js:2806 Loading reference images for action type: doubleClickImageAirtest, target: general
main.js:459 Appium Automation App initialized
main.js:27 ElementInteractions initialized successfully
main.js:43 TestCaseManager module should be loaded globally.
main.js:48 TestCaseManager initialized successfully
main.js:5182 Setting up TestCaseManager dependent event listeners...
main.js:5258 TestCaseManager listeners setup complete.
execution-manager.js:74 ExecutionManager: Event listener added to Execute All button.
execution-manager.js:83 ExecutionManager: Event listener added to Stop button.
main.js:117 ExecutionManager initialized successfully
main.js:132 ActionManager should be initialized in App constructor.
settings.js:7 Settings manager initialized
random-data-generator.js:28 Loaded 22 random data generators
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: true
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
random-data-generator.js:28 Loaded 22 random data generators
main.js:2864 Reference images: Object
main.js:2889 Added 72 reference images to select
execution-manager.js:48 Global values loaded: Object
main.js:2864 Reference images: Object
main.js:2889 Added 72 reference images to select
main.js:2864 Reference images: Object
main.js:2889 Added 72 reference images to select
main.js:2864 Reference images: Object
main.js:2889 Added 72 reference images to select
(index):2784 Current environment response: Object
(index):2916 Selecting environment UI: 5 Current active: null
main.js:2864 Reference images: Object
main.js:2889 Added 72 reference images to select
main.js:2864 Reference images: Object
main.js:2889 Added 72 reference images to select
(index):2831 Populating selectors with active environment: null
main.js:66 Auto-refreshing device list on page load...
main.js:2311 [info] Refreshing device list...
main.js:2311 [success] Found 1 device(s)
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 1/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 2/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 3/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 4/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 5/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 6/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 7/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 8/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 9/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 10/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 11/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:83 Export Manager: Quick check 12/12 for reports
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:1714 Attempting to connect to device: 00008120-00186C801E13C01E, platform: iOS
main.js:2311 [info] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
main.js:1737 Refreshing device screen after connection
main.js:2311 [info] Device info updated: 00008120-00186C801E13C01E
main.js:1749 Device dimensions from connect response: 393x852
main.js:2311 [success] Connected to device: 00008120-00186C801E13C01E with AirTest support
main.js:1819 Refreshing screenshot for device: 00008120-00186C801E13C01E
main.js:2311 [info] Refreshing screenshot...
main.js:1826 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750134155476_68r2mr2lc_1750134155476_68r2mr2lc&t=1750134219743
main.js:2311 [success] Screenshot refreshed
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:1846 Screenshot loaded successfully
main.js:2311 [success] Screenshot refreshed successfully
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:3224 Opening import execution modal
main.js:3239 Got execution reports data: Object
main.js:3317 Selected execution report: Test Run 2025-06-17 14:00:06 (testsuite_execution_20250617_140006)
main.js:3368 Imported execution data: Object
main.js:4242 Cleared currentActions array
main.js:4248 Cleared actionsList UI
main.js:4254 Reset test case name and filename
main.js:2774 [AppiumAutomationApp updateExecutionButtons Instance: 1750134155476_0.8229877373229156] Checking actions array:  Object
main.js:2794 Save button disabled: true
main.js:2799 Save As button disabled: true
main.js:2802 Updated execution buttons. Has actions: false, Has action items: false, Is connected: true
main.js:2311 [info] Cleaning up screenshots...
main.js:2311 [info] All actions cleared
main.js:3728 Loading original test case actions for retry functionality...
main.js:3732 Found 2 unique test cases: Array(2)
main.js:3645 Cleaned test case name: "health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions" -> "health2"
main.js:3600 Loading original test case: health2
main.js:2311 [success] All screenshots deleted successfully
main.js:3609 Loaded test case with 9 actions
main.js:3742 Loaded 9 actions for test case: health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
main.js:3645 Cleaned test case name: "apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            10 actions" -> "apple health"
main.js:3600 Loading original test case: apple health
:8080/api/test_cases/apple%20health:1 
            
            
           Failed to load resource: the server responded with a status of 404 (NOT FOUND)
main.js:3612 Error loading original test case: Error: Failed to load test case: 404
    at AppiumAutomationApp.loadOriginalTestCase (main.js:3605:23)
    at async AppiumAutomationApp.loadOriginalTestCaseActions (main.js:3740:46)
    at async AppiumAutomationApp.importExecutionData (main.js:3377:13)
loadOriginalTestCase @ main.js:3612
main.js:3752 Could not load original actions for test case: apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            10 actions
loadOriginalTestCaseActions @ main.js:3752
main.js:3778 Loaded 19 total actions for retry functionality
main.js:3407 Loading 2 test cases from execution data
main.js:3645 Cleaned test case name: "health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions" -> "health2"
main.js:3645 Cleaned test case name: "apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            10 actions" -> "apple health"
main.js:3542 Error loading test suite from execution data: TypeError: this.updateActionCount is not a function
    at AppiumAutomationApp.loadTestSuiteFromExecutionData (main.js:3537:18)
    at AppiumAutomationApp.importExecutionData (main.js:3383:24)
loadTestSuiteFromExecutionData @ main.js:3542
main.js:3389 Error importing execution data: TypeError: this.updateActionCount is not a function
    at AppiumAutomationApp.loadTestSuiteFromExecutionData (main.js:3537:18)
    at AppiumAutomationApp.importExecutionData (main.js:3383:24)
importExecutionData @ main.js:3389
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:498 Retry button clicked for test case: health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions (File: health2.json)
main.js:2311 [info] Executing action: Launch app: undefined
main.js:2311 [error] Failed to execute action: No bundle ID or package name provided or found in capabilities
main.js:2357 [Action Status] UppP3ZuqY6=fail
main.js:2311 [info] Executing action: Add Log: undefined 
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:2311 [error] Failed to execute action: Missing message parameter
main.js:2357 [Action Status] Successful=fail
main.js:2311 [info] Executing action: Click element: undefined=undefined
main.js:2311 [error] Failed to execute action: Unsupported locator type: 
main.js:2357 [Action Status] XCUIElemen=fail
main.js:2311 [info] Executing action: Add Log: undefined 
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:2311 [error] Failed to execute action: Missing message parameter
main.js:2357 [Action Status] screenshot=fail
main.js:2311 [info] Executing action: Click element: undefined=undefined
main.js:2311 [error] Failed to execute action: Unsupported locator type: 
main.js:2357 [Action Status] XCUIElemen=fail
main.js:2311 [info] Executing action: Add Log: undefined 
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250617_140006
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250617_140006
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250617_140006
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn btn-info me-2">​…​</button>​flex
main.js:2311 [error] Failed to execute action: Missing message parameter
main.js:2357 [Action Status] screenshot=fail
main.js:2311 [info] Executing action: Wait for 1018ms ms
 [error] Failed to execute action: Action execution failed: could not convert string to float: '1018ms'
 [Action Status] ag29wsBP24=fail
 [info] Executing action: Terminate app: undefined
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250617_140006
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250617_140006
   - isExecuting: false
   - latestReportId: testsuite_execution_20250617_140006
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 [error] Failed to execute action: No bundle ID or package name provided or found in capabilities
 [Action Status] vjBGuN5y9x=fail
 [info] Executing action: Add Log: undefined 
 [error] Failed to execute action: Missing message parameter
 [Action Status] screenshot=fail
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250617_140006
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250617_140006
   - isExecuting: false
   - latestReportId: testsuite_execution_20250617_140006
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250617_140006
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250617_140006
   - isExecuting: false
   - latestReportId: testsuite_execution_20250617_140006
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250617_140006
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250617_140006
   - isExecuting: false
   - latestReportId: testsuite_execution_20250617_140006
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250617_140006
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250617_140006
   - isExecuting: false
   - latestReportId: testsuite_execution_20250617_140006
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250617_140006/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250617_140006
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250617_140006
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250617_140006
   - isExecuting: false
   - latestReportId: testsuite_execution_20250617_140006
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
