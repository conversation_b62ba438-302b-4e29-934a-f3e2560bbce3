{"name": "apple health", "description": "Test case for Apple Health app with additional actions", "created": "2025-06-17T14:00:00.000Z", "updated": "2025-06-17T14:00:00.000Z", "actions": [{"type": "launchApp", "action_id": "UppP3ZuqY6", "name": "Launch app: com.apple.Health", "bundleId": "com.apple.Health", "timeout": 10}, {"type": "clickElement", "action_id": "XCUIElemen", "name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "locator": {"type": "xpath", "value": "//XCUIElementTypeStaticText[@name=\"Edit\"]"}, "timeout": 10}, {"type": "takeScreenshot", "action_id": "takeScreen", "name": "takeScreenshot action", "screenshotName": "edit_screen"}, {"type": "clickElement", "action_id": "XCUIElemen2", "name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "locator": {"type": "xpath", "value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]"}, "timeout": 10}, {"type": "clickElement", "action_id": "XCUIElemen3", "name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "locator": {"type": "xpath", "value": "//XCUIElementTypeStaticText[@name=\"Edit\"]"}, "timeout": 10}, {"type": "addLog", "action_id": "successful", "name": "Add Log: Clicked on Edit link successfully (with screenshot)", "message": "Clicked on Edit link successfully", "screenshot": true}, {"type": "clickElement", "action_id": "XCUIElemen4", "name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "locator": {"type": "xpath", "value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]"}, "timeout": 10}, {"type": "terminateApp", "action_id": "ag29wsBP24", "name": "Terminate app: com.apple.Health", "bundleId": "com.apple.Health", "timeout": 10}, {"type": "takeScreenshot", "action_id": "takeScreen2", "name": "takeScreenshot action", "screenshotName": "app_closed"}, {"type": "addLog", "action_id": "screenshot", "name": "Add Log: App is closed (with screenshot)", "message": "App is closed", "screenshot": true}]}