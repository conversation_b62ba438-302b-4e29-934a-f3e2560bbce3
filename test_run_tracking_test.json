{"name": "Test Run Tracking Test", "description": "Simple test to verify test run tracking functionality", "actions": [{"action_id": "al_test_start", "type": "addLog", "message": "Starting test run tracking test", "take_screenshot": true, "timestamp": 1745821830633}, {"action_id": "al_test_middle", "type": "addLog", "message": "Middle of test execution", "take_screenshot": true, "timestamp": 1745821830634}, {"action_id": "al_test_end", "type": "addLog", "message": "Test run tracking test completed", "take_screenshot": true, "timestamp": 1745821830635}]}